{"name": "smart-city-os", "version": "1.0.0", "description": "A comprehensive Smart City Operating System with IoT integration, predictive analytics, and blockchain transparency", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "node backend/server.js", "frontend": "cd frontend && npm start", "frontend:build": "cd frontend && npm run build", "frontend:install": "cd frontend && npm install", "iot-sim": "cd iot-simulation && python3 main.py", "iot-sim:install": "cd iot-simulation && pip3 install -r requirements.txt", "analytics": "cd analytics && python app.py", "test": "node test-setup.js", "test:full": "node test-full-system.js", "build": "cd frontend && npm run build", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm install && npm run frontend:install && npm run iot-sim:install"}, "keywords": ["smart-city", "iot", "blockchain", "predictive-analytics", "urban-management"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"axios": "^1.4.0", "bcryptjs": "^2.4.3", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "morgan": "^1.10.0", "node-cron": "^3.0.2", "nodemailer": "^6.9.4", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "rate-limiter-flexible": "^2.4.2", "sequelize": "^6.32.1", "socket.io": "^4.7.2"}, "devDependencies": {"@types/jest": "^29.5.4", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}